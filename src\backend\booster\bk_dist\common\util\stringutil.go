/*
 * Copyright (c) 2021 THL A29 Limited, a Tencent company. All rights reserved
 *
 * This source code file is licensed under the MIT License, you may obtain a copy of the License at
 *
 * http://opensource.org/licenses/MIT
 *
 */

package util

import (
	"bufio"
	"io/ioutil"
	"os"
	"strings"

	"github.com/saintfish/chardet"
	"golang.org/x/text/encoding/unicode"
)

// HasSpace 检查字符串是否包含空格
func HasSpace(s string) bool {
	if s == "" {
		return false
	}

	for _, v := range s {
		if v == ' ' {
			return true
		}
	}

	return false
}

// CheckCharset 检测字节数组的字符集
func CheckCharset(rawBytes []byte) (string, error) {
	detector := chardet.NewTextDetector()
	charset, err := detector.DetectBest(rawBytes)
	if err != nil {
		return "", err
	}

	return charset.Charset, nil
}

// CheckFileCharset 检测文件的字符集
func CheckFileCharset(f string) (string, error) {
	data, err := ioutil.ReadFile(f)
	if err != nil {
		return "", err
	}

	return CheckCharset(data)
}

// ReadBom 读取UTF-16LE格式的文件
func ReadBom(filename string) (string, error) {
	f, err := os.Open(filename)
	if err != nil {
		return "", err
	}
	defer f.Close()

	dec := unicode.UTF16(unicode.LittleEndian, unicode.UseBOM).NewDecoder()
	scn := bufio.NewScanner(dec.Reader(f))
	data := ""
	for scn.Scan() {
		data = data + scn.Text()
	}
	if err := scn.Err(); err != nil {
		return "", err
	}

	return data, nil
}

// ReadUtf8 读取UTF-8格式的文件
func ReadUtf8(filename string) (string, error) {
	data, err := ioutil.ReadFile(filename)
	if err != nil {
		return "", err
	}

	return string(data), nil
}

// ReplaceWithNextExclude 替换字符串中的字符，但排除某些后续字符
func ReplaceWithNextExclude(s string, old byte, new string, nextExcludes []byte) string {
	if s == "" {
		return ""
	}

	if len(nextExcludes) == 0 {
		return strings.Replace(s, string(old), new, -1)
	}

	result := make([]byte, 0, len(s)*2)
	totallen := len(s)

	for i := 0; i < totallen; i++ {
		c := s[i]
		if c == old {
			nextExclude := false
			if i < totallen-1 {
				next := s[i+1]
				for _, e := range nextExcludes {
					if next == e {
						nextExclude = true
						break
					}
				}
			}

			if nextExclude {
				result = append(result, c, s[i+1])
				i++
			} else {
				result = append(result, []byte(new)...)
			}
		} else {
			result = append(result, c)
		}
	}

	return string(result)
}

// HasOptions 检查字符串数组中是否包含指定字符串
func HasOptions(r []string, s string) bool {
	for _, i := range r {
		if i == s {
			return true
		}
	}

	return false
}
