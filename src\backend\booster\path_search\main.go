package main

import (
go.mod "bufio"
go.mod "fmt"
go.mod "io/fs"
go.mod "os"
go.mod "path/filepath"
go.mod "strings"
go.mod "sync"
go.mod "time"
)

// 用于存储结果的结构
type Result struct {
go.mod FilesProcessed int
go.mod UniquePathsMap map[string]bool
go.mod mu             sync.Mutex
}

// 添加路径到结果集
func (r *Result) AddPath(path string) {
go.mod r.mu.Lock()
go.mod defer r.mu.Unlock()
go.mod r.UniquePathsMap[path] = true
}

// 增加处理文件计数
func (r *Result) IncrementFilesProcessed() {
go.mod r.mu.Lock()
go.mod defer r.mu.Unlock()
go.mod r.FilesProcessed++
}

func main() {
go.mod startTime := time.Now()

go.mod // 获取用户主目录
go.mod userProfile := os.Getenv("USERPROFILE")
go.mod if userProfile == "" {
go.mod go.mod fmt.Println("无法获取用户主目录环境变量 %USERPROFILE%")
go.mod go.mod return
go.mod }

go.mod // 构建日志目录路径
go.mod logsDir := filepath.Join(userProfile, ".bk_dist", "logs")
go.mod fmt.Printf("搜索目录: %s\n", logsDir)

go.mod // 检查目录是否存在
go.mod _, err := os.Stat(logsDir)
go.mod if os.IsNotExist(err) {
go.mod go.mod fmt.Printf("目录不存在: %s\n", logsDir)
go.mod go.mod return
go.mod }

go.mod // 初始化结果
go.mod result := &Result{
go.mod go.mod FilesProcessed: 0,
go.mod go.mod UniquePathsMap: make(map[string]bool),
go.mod }

go.mod // 使用WalkDir遍历目录及其子目录
go.mod err = filepath.WalkDir(logsDir, func(path string, d fs.DirEntry, err error) error {
go.mod go.mod if err != nil {
go.mod go.mod go.mod fmt.Printf("访问路径 %s 时出错: %v\n", path, err)
go.mod go.mod go.mod return nil // 继续处理其他文件
go.mod go.mod }

go.mod go.mod // 跳过目录
go.mod go.mod if d.IsDir() {
go.mod go.mod go.mod return nil
go.mod go.mod }

go.mod go.mod // 处理文件
go.mod go.mod processFile(path, result)
go.mod go.mod return nil
go.mod })

go.mod if err != nil {
go.mod go.mod fmt.Printf("遍历目录失败: %v\n", err)
go.mod go.mod return
go.mod }

go.mod // 将结果转换为切片以便排序和打印
go.mod var uniquePaths []string
go.mod for path := range result.UniquePathsMap {
go.mod go.mod uniquePaths = append(uniquePaths, path)
go.mod }

go.mod // 打印结果
go.mod fmt.Printf("\n处理了 %d 个文件，找到 %d 个唯一路径:\n", result.FilesProcessed, len(uniquePaths))
go.mod for _, path := range uniquePaths {
go.mod go.mod fmt.Println(path)
go.mod }

go.mod // 打印执行时间
go.mod fmt.Printf("\n执行耗时: %v\n", time.Since(startTime))
}

// 处理单个文件
func processFile(filePath string, result *Result) {
go.mod // 打开文件
go.mod file, err := os.Open(filePath)
go.mod if err != nil {
go.mod go.mod fmt.Printf("打开文件 %s 失败: %v\n", filePath, err)
go.mod go.mod return
go.mod }
go.mod defer file.Close()

go.mod // 增加处理文件计数
go.mod result.IncrementFilesProcessed()

go.mod // 创建扫描器
go.mod scanner := bufio.NewScanner(file)
go.mod 
go.mod // 设置较大的缓冲区以处理长行
go.mod const maxCapacity = 512 * 1024 // 512KB
go.mod buf := make([]byte, maxCapacity)
go.mod scanner.Buffer(buf, maxCapacity)

go.mod // 逐行读取文件
go.mod lineNum := 0
go.mod for scanner.Scan() {
go.mod go.mod lineNum++
go.mod go.mod line := scanner.Text()

go.mod go.mod // 查找包含 "LocalFullPath:" 的行
go.mod go.mod if idx := strings.Index(line, "LocalFullPath:"); idx >= 0 {
go.mod go.mod go.mod // 提取路径部分
go.mod go.mod go.mod pathPart := line[idx+len("LocalFullPath:"):]
go.mod go.mod go.mod 
go.mod go.mod go.mod // 处理可能的引号或其他分隔符
go.mod go.mod go.mod path := cleanPath(pathPart)
go.mod go.mod go.mod 
go.mod go.mod go.mod // 如果路径不为空，添加到结果中
go.mod go.mod go.mod if path != "" {
go.mod go.mod go.mod go.mod result.AddPath(path)
go.mod go.mod go.mod }
go.mod go.mod }
go.mod }

go.mod // 检查扫描错误
go.mod if err := scanner.Err(); err != nil {
go.mod go.mod fmt.Printf("读取文件 %s 时出错: %v\n", filePath, err)
go.mod }
}

// 清理和规范化路径
func cleanPath(pathPart string) string {
go.mod // 去除前后空格
go.mod path := strings.TrimSpace(pathPart)
go.mod 
go.mod // 处理可能的引号
go.mod if strings.HasPrefix(path, "\"") && strings.HasSuffix(path, "\"") {
go.mod go.mod path = path[1 : len(path)-1]
go.mod }
go.mod 
go.mod // 处理可能的逗号或分号结尾
go.mod path = strings.TrimRight(path, ",;")
go.mod 
go.mod // 处理可能的日志格式特殊字符
go.mod if idx := strings.Index(path, " "); idx > 0 {
go.mod go.mod // 如果空格后面是日志格式的内容，只保留空格前的部分
go.mod go.mod if strings.ContainsAny(path[idx:], "[]{}()") {
go.mod go.mod go.mod path = path[:idx]
go.mod go.mod }
go.mod }
go.mod 
go.mod return path
}
